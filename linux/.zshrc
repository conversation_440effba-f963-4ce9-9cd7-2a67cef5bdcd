# Enable Powerlevel10k instant prompt. Should stay close to the top of ~/.zshrc.
# Initialization code that may require console input (password prompts, [y/n]
# confirmations, etc.) must go above this block; everything else may go below.
if [[ -r "${XDG_CACHE_HOME:-$HOME/.cache}/p10k-instant-prompt-${(%):-%n}.zsh" ]]; then
  source "${XDG_CACHE_HOME:-$HOME/.cache}/p10k-instant-prompt-${(%):-%n}.zsh"
fi

# If you come from bash you might have to change your $PATH.
# export PATH=$HOME/bin:/usr/local/bin:$PATH

# Path to your oh-my-zsh installation.
export ZSH="/home/<USER>/.oh-my-zsh"

# Set name of the theme to load --- if set to "random", it will
# load a random theme each time oh-my-zsh is loaded, in which case,
# to know which specific one was loaded, run: echo $RANDOM_THEME
# See https://github.com/ohmyzsh/ohmyzsh/wiki/Themes
ZSH_THEME="powerlevel10k/powerlevel10k"

# Set list of themes to pick from when loading at random
# Setting this variable when ZSH_THEME=random will cause zsh to load
# a theme from this variable instead of looking in $ZSH/themes/
# If set to an empty array, this variable will have no effect.
# ZSH_THEME_RANDOM_CANDIDATES=( "robbyrussell" "agnoster" )

# Uncomment the following line to use case-sensitive completion.
# CASE_SENSITIVE="true"

# Uncomment the following line to use hyphen-insensitive completion.
# Case-sensitive completion must be off. _ and - will be interchangeable.
# HYPHEN_INSENSITIVE="true"

# Uncomment the following line to disable bi-weekly auto-update checks.
# DISABLE_AUTO_UPDATE="true"

# Uncomment the following line to automatically update without prompting.
# DISABLE_UPDATE_PROMPT="true"

# Uncomment the following line to change how often to auto-update (in days).
# export UPDATE_ZSH_DAYS=13

# Uncomment the following line if pasting URLs and other text is messed up.
# DISABLE_MAGIC_FUNCTIONS="true"

# Uncomment the following line to disable colors in ls.
# DISABLE_LS_COLORS="true"

# Uncomment the following line to disable auto-setting terminal title.
# DISABLE_AUTO_TITLE="true"

# Uncomment the following line to enable command auto-correction.
# ENABLE_CORRECTION="true"

# Uncomment the following line to display red dots whilst waiting for completion.
# Caution: this setting can cause issues with multiline prompts (zsh 5.7.1 and newer seem to work)
# See https://github.com/ohmyzsh/ohmyzsh/issues/5765
# COMPLETION_WAITING_DOTS="true"

# Uncomment the following line if you want to disable marking untracked files
# under VCS as dirty. This makes repository status check for large repositories
# much, much faster.
# DISABLE_UNTRACKED_FILES_DIRTY="true"

# Uncomment the following line if you want to change the command execution time
# stamp shown in the history command output.
# You can set one of the optional three formats:
# "mm/dd/yyyy"|"dd.mm.yyyy"|"yyyy-mm-dd"
# or set a custom format using the strftime function format specifications,
# see 'man strftime' for details.
# HIST_STAMPS="mm/dd/yyyy"

# Would you like to use another custom folder than $ZSH/custom?
# ZSH_CUSTOM=/path/to/new-custom-folder

# Which plugins would you like to load?
# Standard plugins can be found in $ZSH/plugins/
# Custom plugins may be added to $ZSH_CUSTOM/plugins/
# Example format: plugins=(rails git textmate ruby lighthouse)
# Add wisely, as too many plugins slow down shell startup.
plugins=(git zsh-syntax-highlighting)

source $ZSH/oh-my-zsh.sh
# User configuration

# export MANPATH="/usr/local/man:$MANPATH"

# You may need to manually set your language environment
# export LANG=en_US.UTF-8

# Preferred editor for local and remote sessions
# if [[ -n $SSH_CONNECTION ]]; then
#   export EDITOR='vim'
# else
#   export EDITOR='mvim'
# fi

# Compilation flags
# export ARCHFLAGS="-arch x86_64"

# Set personal aliases, overriding those provided by oh-my-zsh libs,
# plugins, and themes. Aliases can be placed here, though oh-my-zsh
# users are encouraged to define aliases within the ZSH_CUSTOM folder.
# For a full list of active aliases, run `alias`.
#
# Example aliases
# alias zshconfig="mate ~/.zshrc"
# alias ohmyzsh="mate ~/.oh-my-zsh"

alias mrsc='sudo mount -t cifs -o username=qadm,password=P@sswd12,uid=$(id -u),gid=$(id -g),file_mode=0777,dir_mode=0777 //rsc.home.qwrobins.net/data /mnt/data'
export PYENV_ROOT="$HOME/.pyenv"
export PATH="$PYENV_ROOT/bin:$PATH"
export GOPATH="/home/<USER>/development/go"
export GO111MODULE="on"
export KUBECONFIG=$KUBECONFIG:$HOME/.kube/config:$HOME/development/vcs-terraform/k8s/rke/cis-rke/kube_config_cluster.yml:$HOME/development/vcs-terraform/k8s/rke/on-prem-rke/kube_config_cluster.yml
alias k='/usr/local/bin/kubectl'
alias kaf='kubectl apply -f'
alias kdf='kubectl delete -f'
alias kgpa='watch kubectl get pods --all-namespaces -o wide'
alias kgps='watch kubectl get services --all-namespaces'
alias kgda='kubectl get pods --all-namespaces'
alias kcn='kubectl config set-context  --namespace'
alias ekc='code ~/.kube/config'
alias trg='/usr/local/bin/terragrunt'
alias trf='/usr/local/bin/terraform'
alias ckc="cp $HOME/development/vcs-terraform/main-rke/kube_config_cluster.yml ~/.kube/config"
alias gpush="git add -A && git commit -am 'update' && git push"
alias acdloc="argocd login localhost:8080"
alias acdlhom="argocd login home.qwrobins.net:8080"
alias acdcp="argocd account update-password"
alias apb='ansible-playbook'
alias qiam='aws-vault --debug exec qwr-iam --duration=12h --no-session $SHELL'
alias ukc='aws eks update-kubeconfig --name eks-cluster'

# Gloo
alias prod='aws-vault --debug exec gloo-engineering-prod --duration=12h --no-session $SHELL'
alias lprod='aws-vault --debug exec --server --prompt=osascript gloo-engineering-prod --duration=12h $SHELL'
alias sbox='aws-vault --debug exec gloo-sandbox --duration=12h --no-session $SHELL'
alias lens='open -a Lens'

export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && . "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
export EDITOR='code --wait'

# To customize prompt, run `p10k configure` or edit ~/.p10k.zsh.
[[ ! -f ~/.p10k.zsh ]] || source ~/.p10k.zsh
export PYENV_ROOT="$HOME/.pyenv"
export PATH="$PYENV_ROOT/bin:$HOME/development/go/bin:/opt/graalvm-ce-java11-********/bin:$PATH"
export GOPATH="/home/<USER>/development/go"
export GO111MODULE="on"
eval "$(rbenv init -)"

alias ednv='code /media/qwrobins/970-EVO/Linux/envsetup.sh'
export GO111MODULE=on
export PATH=$PATH:/usr/local/go/bin:/home/<USER>/development/go/bin
export GOPATH=/home/<USER>/development/go
export PATH="$HOME/.rbenv/bin:$PATH"
export JAVA_HOME=/opt/graalvm-ce-java11-********
export PATH="${KREW_ROOT:-$HOME/.krew}/bin:$PATH"
eval $(/home/<USER>/.linuxbrew/bin/brew shellenv)
