#!/bin/bash

# setup data share directory
if [[ ! -d $HOME/Data ]]; then
  mkdir -p $HOME/Data
fi

# mount data share
mount_smbfs //<EMAIL>/data $HOME/Data

# Setup ssh
if [[ ! -d $HOME/.ssh ]]; then
  mkdir -p $HOME/.ssh && cp -R $HOME/Data/ssh/* $HOME/.ssh && chmod -R 0700 $HOME/.ssh && chmod 0600 $HOME/.ssh/id_rsa && chmod 0644 $HOME/.ssh/id_rsa.pub $HOME/.ssh/config
fi

# AWS setup
if [[ ! -f $HOME/.aws/credentials ]]; then
  if [[ ! -d $HOME/.aws ]]; then
    mkdir -p $HOME/.aws
  fi

  cp $HOME/Data/Development/aws/ $HOME/.aws
fi
# copy ovpn file
if [[ ! -f $HOME/qwr.ovpn ]]; then
  cp $HOME/Data/qwr.ovpn $HOME/qwr.ovpn
fi

if [[ ! -d $HOME/Pictures/wallpapers ]]; then
  mkdir -p $HOME/Pictures/wallpapers
  cp $HOME/Data/WP/4K/* $HOME/Pictures/wallpapers/
fi

# check git install
git

# setup zsh
if [[ ! -d $HOME/.oh-my-zsh ]]; then
  sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"
  chmod 755 /usr/local/share/zsh
  chmod 755 /usr/local/share/zsh/site-functions
  git clone https://github.com/zsh-users/zsh-syntax-highlighting.git "$HOME/.zsh-syntax-highlighting" --depth 1
  echo "source $HOME/.zsh-syntax-highlighting/zsh-syntax-highlighting.zsh" >> "$HOME/.zshrc"
  git clone https://github.com/lukechilds/zsh-nvm ~/.oh-my-zsh/custom/plugins/zsh-nvm
fi

# install xcode command line tools
xcode-select --install

# install homebrew
if [[ `which brew` == '' ]]; then
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install.sh)"
fi

# Download apps
if [[ `which wget` == '' ]]; then
  brew install wget
fi

if [[ ! -f $HOME/Downloads/Docker.dmg ]]; then
  cd $HOME/Downloads
  wget https://download.docker.com/mac/stable/Docker.dmg
  wget https://downloads.nordcdn.com/apps/macos/generic/NordLocker/latest/NordLocker.pkg # Nordlocker
  wget https://download3.vmware.com/software/fusion/file/VMware-Fusion-12.1.2-17964953.dmg # Fusion
  wget https://golang.org/dl/go1.16.5.darwin-amd64.pkg # golang
fi

# ruby dependencies
if [[ ! -f $HOME/.gemrc ]]; then
  echo "gem: --no-document" >> ~/.gemrc
  brew install rbenv ruby-build
fi

# Install brew apps
brew install warrensbox/tap/tfswitch kops gopass watch zsh-syntax-highlighting helm macvim terragrunt maven pyenv jq kubectl kubeseal aws-iam-authenticator cuelang/tap/cue aws-vault eksctl kubectx argo argocd alacritty k9s kind rke derailed/k9s/k9s awscli rust yq istioctl stern google-cloud-sdk dotnet
 
# casks
brew install --cask visual-studio-code postman waterfox microsoft-edge expandrive iterm2 session-manager-plugin mongodb-compass datagrip gitkraken session-manager-plugin lens firefox

# Install Taps
brew tap argoproj/tap && brew install argoproj/tap/argocd
brew tap AdoptOpenJDK/openjdk && brew install --cask adoptopenjdk11
brew tap mongodb/brew && brew install mongodb-community
brew services start mongodb-community

# Aliases
if [[ `cat $HOME/.zshrc | grep kgpa` == '' ]]; then
  mkdir -p ~/.kube
  touch ~/.kube/config
fi

# Repos
if [[ ! -d $HOME/Development ]]; then
  mkdir -p $HOME/Development
fi

cd $HOME/Development
if [[ ! -d dotfiles ]]; then
  <NAME_EMAIL>:q6-all/dotfiles.git
  cp dotfiles/macos/* $HOME
fi

if [[ ! -d terraform ]]; then
  <NAME_EMAIL>:qwrobins/terraform.git
fi

if [[ ! -d ansible ]]; then
  <NAME_EMAIL>:qwrobins/ansible.git
fi

if [[ ! -d vcs-terraform ]]; then
  <NAME_EMAIL>:q6-all/vcs-terraform.git
fi

if [[ ! -d kind ]]; then
  <NAME_EMAIL>:q6/kind.git
fi

if [[ ! -d k8s ]]; then
  <NAME_EMAIL>:qwrobins/k8s.git
fi

if [[ ! -d argocd ]]; then
  mkdir $HOME/Development/argocd && cd $HOME/Development/argocd
  <NAME_EMAIL>:q6-all/on-prem-manifests.git
  <NAME_EMAIL>:q6-all/mgmt-manifests.git
  <NAME_EMAIL>:q6-all/eks-manifests.git
  cd $HOME/Development
fi

if [[ ! -d idm ]]; then
  <NAME_EMAIL>:q6-all/idm.git
fi

if [[ ! -d code ]]; then
  <NAME_EMAIL>:q6-all/code.git
fi

if [[ ! -d postgres ]]; then
  <NAME_EMAIL>:q6-all/postgres.git
fi

if [[ ! -d runner ]]; then
  <NAME_EMAIL>:q6-all/runner.git
fi

if [[ ! -d $HOME/Development/Docker ]]; then
  mkdir -p $HOME/Development/Docker && cd $HOME/Development/Docker
  <NAME_EMAIL>:q6-all/k8s-util.git
  cd $HOME/Development
fi

if [[ ! -d $HOME/Development/Angular ]]; then
  mkdir -p $HOME/Development/Angular && cd $HOME/Development/Angular
  <NAME_EMAIL>:q6-all/qwrnet.git
  <NAME_EMAIL>:q6-all/angular-crash-course.git
  <NAME_EMAIL>:qwrobins/acc.git
  <NAME_EMAIL>:q6-all/wa-fe.git
  <NAME_EMAIL>:q6-all/bottleneck.git
  cd $HOME/Development
fi

if [[ ! -d $HOME/Development/go ]]; then
  mkdir $HOME/Development/go && cd $HOME/Development/go
  <NAME_EMAIL>:q6/go-api.git
  <NAME_EMAIL>:q6/keygen.git
  <NAME_EMAIL>:q6/pic-scraper.git
  <NAME_EMAIL>:q6-all/get-apps.git
  <NAME_EMAIL>:q6-all/wa-api.git
  <NAME_EMAIL>:q6-all/feeding-api.git
  <NAME_EMAIL>:q6/spinner.git
  cd $HOME/Development
fi

if [[ ! -d $HOME/Development/buffalo ]]; then
  mkdir -p $HOME/Development/buffalo && cd $HOME/Development/buffalo
  <NAME_EMAIL>:qwrobins/wedding.git
  <NAME_EMAIL>:q6-all/wedding-album.git
  cd $HOME/Development
fi

if [[ ! -d $HOME/Development/Rails ]]; then
  mkdir -p $HOME/Development/Rails
  cd Rails
  <NAME_EMAIL>:q6-all/wa-api.git
  cd $HOME/Development
fi

if [[ ! -d $HOME/Development/React ]]; then
  mkdir -p $HOME/Development/React
  cd React
  <NAME_EMAIL>:q6-all/wa-fe.git
  cd $HOME/Development
fi

if [[ ! -d $HOME/Development/Gloo ]]; then
  mkdir -p $HOME/Development/Gloo
  cd Gloo
  <NAME_EMAIL>:TangoGroup/sre.git
  <NAME_EMAIL>:TangoGroup/stx.git
  <NAME_EMAIL>:TangoGroup/gjp-k8s.git
  <NAME_EMAIL>:TangoGroup/gloo-engineering-terraform.git
  cd $HOME/Development
fi

if [[ ! -d  ~/.nvm ]]; then
  curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.35.1/install.sh | zsh
  export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
  nvm install --lts
fi

git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/themes/powerlevel10k

