#!/bin/bash

# Development Environment Backup Script - Robust Version
# This script scans for Git repositories and generates a restoration script
# Author: Generated for development environment backup
# Usage: ./backup-dev-env-robust.sh [output_script_name]

# Remove strict error handling to prevent early exit
set -uo pipefail

# Configuration
SCRIPT_NAME="${1:-restore-dev-env.sh}"
BACKUP_DATE=$(date '+%Y-%m-%d_%H-%M-%S')
LOG_FILE="backup-scan-${BACKUP_DATE}.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
    log "INFO: $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    log "WARN: $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    log "ERROR: $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
    log "HEADER: $1"
}

# Function to get remote URL for a repository
get_remote_url() {
    local repo_path="$1"
    local remote_url=""
    
    if cd "$repo_path" 2>/dev/null; then
        # Try to get origin remote first
        if git remote get-url origin >/dev/null 2>&1; then
            remote_url=$(git remote get-url origin 2>/dev/null || echo "")
        else
            # If no origin, get the first available remote
            local first_remote
            first_remote=$(git remote 2>/dev/null | head -n1 || echo "")
            if [[ -n "$first_remote" ]]; then
                remote_url=$(git remote get-url "$first_remote" 2>/dev/null || echo "")
                print_warning "Repository at $repo_path has no 'origin' remote, using '$first_remote': $remote_url"
            fi
        fi
        cd - >/dev/null 2>&1
    fi
    
    echo "$remote_url"
}

# Function to get current branch
get_current_branch() {
    local repo_path="$1"
    local current_branch="main"
    
    if cd "$repo_path" 2>/dev/null; then
        if git symbolic-ref --short HEAD >/dev/null 2>&1; then
            current_branch=$(git symbolic-ref --short HEAD 2>/dev/null || echo "main")
        elif git describe --exact-match HEAD >/dev/null 2>&1; then
            current_branch="$(git describe --exact-match HEAD 2>/dev/null || echo "main") (tag)"
        else
            current_branch="(detached HEAD)"
        fi
        cd - >/dev/null 2>&1
    fi
    
    echo "$current_branch"
}

# Function to check if repository has uncommitted changes
has_uncommitted_changes() {
    local repo_path="$1"
    local has_changes=false
    
    if cd "$repo_path" 2>/dev/null; then
        # Check for staged changes
        if ! git diff --cached --quiet 2>/dev/null; then
            has_changes=true
        fi
        
        # Check for unstaged changes
        if ! git diff --quiet 2>/dev/null; then
            has_changes=true
        fi
        
        # Check for untracked files
        if [[ -n $(git ls-files --others --exclude-standard 2>/dev/null) ]]; then
            has_changes=true
        fi
        
        cd - >/dev/null 2>&1
    fi
    
    if [[ "$has_changes" == "true" ]]; then
        return 0
    else
        return 1
    fi
}

# Main function to scan repositories
scan_repositories() {
    local base_dir
    base_dir=$(pwd)
    local repo_count=0
    local skipped_count=0
    
    print_header "Scanning for Git repositories in: $base_dir"
    
    # Create restoration script header
    cat > "$SCRIPT_NAME" << 'EOF'
#!/bin/bash

# Development Environment Restoration Script
# Generated automatically by backup-dev-env-robust.sh
# This script recreates the development folder structure by cloning repositories

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# Function to clone repository with error handling
clone_repo() {
    local url="$1"
    local path="$2"
    local branch="$3"
    
    print_status "Cloning $url to $path"
    
    # Create parent directory if it doesn't exist
    mkdir -p "$(dirname "$path")"
    
    if git clone "$url" "$path"; then
        print_status "Successfully cloned $path"
        
        # Try to checkout the original branch if it's not the default
        if [[ "$branch" != "main" && "$branch" != "master" && "$branch" != "(detached"* ]]; then
            cd "$path"
            if git checkout "$branch" 2>/dev/null; then
                print_status "Checked out branch: $branch"
            else
                print_warning "Could not checkout branch '$branch', staying on default branch"
            fi
            cd - >/dev/null
        fi
    else
        print_error "Failed to clone $url to $path"
        return 1
    fi
}

print_header "Starting development environment restoration..."
print_status "Base directory: $(pwd)"
echo

EOF

    # Find all .git directories and process them (excluding Terraform modules)
    print_status "Finding Git repositories..."
    local git_dirs=()
    while IFS= read -r -d '' git_dir; do
        # Skip Terraform module directories
        if [[ "$git_dir" == *"/.terraform/modules/"* ]]; then
            continue
        fi
        git_dirs+=("$git_dir")
    done < <(find "$base_dir" -name ".git" -type d -print0)
    
    print_status "Found ${#git_dirs[@]} Git repositories to process"
    
    local processed=0
    for git_dir in "${git_dirs[@]}"; do
        processed=$((processed + 1))
        local repo_path
        repo_path=$(dirname "$git_dir")
        local relative_path
        relative_path=$(realpath --relative-to="$base_dir" "$repo_path" 2>/dev/null || echo "$repo_path")
        
        print_status "Processing repository $processed/${#git_dirs[@]}: $relative_path"
        
        # Get remote URL with error handling
        local remote_url
        remote_url=$(get_remote_url "$repo_path")
        
        if [[ -z "$remote_url" ]]; then
            print_warning "Skipping $relative_path - no remote URL found"
            ((skipped_count++))
            continue
        fi
        
        # Get current branch
        local current_branch
        current_branch=$(get_current_branch "$repo_path")
        
        # Check for uncommitted changes
        local changes_status=""
        if has_uncommitted_changes "$repo_path"; then
            changes_status=" (HAS UNCOMMITTED CHANGES)"
            print_warning "Repository $relative_path has uncommitted changes"
        fi
        
        # Add to restoration script
        cat >> "$SCRIPT_NAME" << EOF
# Repository: $relative_path
# Remote URL: $remote_url
# Current branch: $current_branch$changes_status
clone_repo "$remote_url" "$relative_path" "$current_branch"
echo

EOF
        
        ((repo_count++))
        
    done
    
    # Add footer to restoration script
    cat >> "$SCRIPT_NAME" << 'EOF'
print_header "Development environment restoration completed!"
print_status "All repositories have been processed."
print_warning "Remember to:"
print_warning "  1. Review any repositories that had uncommitted changes"
print_warning "  2. Set up any necessary environment variables"
print_warning "  3. Install dependencies for each project"
print_warning "  4. Configure any local settings or secrets"
EOF

    # Make restoration script executable
    chmod +x "$SCRIPT_NAME"
    
    print_header "Scan completed!"
    print_status "Found $repo_count repositories with remotes"
    print_warning "Skipped $skipped_count repositories without remotes"
    print_status "Restoration script created: $SCRIPT_NAME"
    print_status "Log file created: $LOG_FILE"
    echo
    print_status "To restore your development environment:"
    print_status "  1. Copy $SCRIPT_NAME to your new system"
    print_status "  2. Run: ./$SCRIPT_NAME"
}

# Main execution
main() {
    print_header "Development Environment Backup Tool - Robust Version"
    print_status "Starting backup scan at $(date)"
    echo
    
    scan_repositories
    
    print_status "Backup scan completed at $(date)"
}

# Run main function
main "$@"
