# Use powerline
USE_POWERLINE="true"
# Source manjaro-zsh-configuration
if [[ -e /usr/share/zsh/manjaro-zsh-config ]]; then
  source /usr/share/zsh/manjaro-zsh-config
fi
# Use manjaro zsh prompt
if [[ -e /usr/share/zsh/manjaro-zsh-prompt ]]; then
  source /usr/share/zsh/manjaro-zsh-prompt
fi

alias mrsc='mount_smbfs //<EMAIL>/data /Users/<USER>/Data'
export KUBECONFIG=$KUBECONFIG:$HOME/.kube/config:$HOME/Development/vcs-terraform/k8s/rke/cis-rke/kube_config_cluster.yml:$HOME/Development/vcs-terraform/k8s/rke/on-prem-rke/kube_config_cluster.yml:/$HOME/Development/vcs-terraform/k8s/rke/mgmt-rke/kube_config_cluster.yml
alias k='/usr/bin/kubectl'
alias kaf='kubectl apply -f'
alias kdf='kubectl delete -f'
alias kgpa='watch kubectl get pods --all-namespaces -o wide'
alias kgps='watch kubectl get services --all-namespaces'
alias kgda='kubectl get pods --all-namespaces'
alias kcn='kubectl config set-context  --namespace'
alias ekc='code ~/.kube/config'
alias trg='/usr/bin/terragrunt'
alias trf='/usr/bin/terraform'
alias ckc="cp $HOME/Development/vcs-terraform/main-rke/kube_config_cluster.yml ~/.kube/config"
alias gpush="git add -A && git commit -am 'update' && git push"
alias acdlg="argocd login localhost:8080"
alias acdcp="argocd account update-password"
alias apb='ansible-playbook'
alias qiam='aws-vault --debug exec qwr-iam --duration=12h --no-session $SHELL'
alias ukc='aws eks update-kubeconfig --name eks-cluster'
eval "$(rbenv init -)"

# Gloo
alias prod='aws-vault --debug exec gloo-engineering-prod --duration=12h --no-session $SHELL'
alias lprod='aws-vault --debug exec --server --prompt=osascript gloo-engineering-prod --duration=12h $SHELL'
alias sbox='aws-vault --debug exec power-user-sandbox --duration=12h --no-session $SHELL'
alias lens='open -a Lens'

export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && . "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
export EDITOR='code --wait'

# To customize prompt, run `p10k configure` or edit ~/.p10k.zsh.
[[ ! -f ~/.p10k.zsh ]] || source ~/.p10k.zsh
export PYENV_ROOT="$HOME/.pyenv"
export PATH="$PYENV_ROOT/bin:$HOME/Development/go/bin:$PATH"
export JAVA_HOME="/Library/Java/JavaVirtualMachines/graalvm-ce-java11-********/Contents/Home"
export GOPATH="/Users/<USER>/Development/go"
export GO111MODULE="on"
eval "$(rbenv init -)"
source /Users/<USER>/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh